# 🎓 ACU AI Career Coach

An AI-powered career exploration platform designed for Australian Catholic University (ACU) recruitment events. This interactive web application helps high school students discover their dream careers and create personalized career roadmaps through an engaging 6-step journey.

## ✨ Features

- **Interactive Multi-Step Form**: Guided 6-step process collecting student information, career aspirations, and interests
- **AI Career Coaching**: ChatGPT-style AI interface for personalized career guidance and Q&A
- **Personalized Career Roadmaps**: Custom career recommendations sent directly to students' email
- **Responsive Design**: Optimized for desktop and tablet use during recruitment events
- **Real-time Progress Tracking**: Visual progress indicator showing student journey completion
- **Terms & Conditions Integration**: Built-in privacy policy and terms acceptance workflow

## 🚀 Technology Stack

- **Framework**: [Next.js 14](https://nextjs.org/) with App Router
- **Language**: [TypeScript](https://www.typescriptlang.org/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **UI Components**: [shadcn/ui](https://ui.shadcn.com/)
- **Image Optimization**: Next.js Image component with performance optimizations
- **Font**: Montserrat (headings), Inter (UI elements)

## 📋 Prerequisites

- Node.js 18.x or later
- npm or yarn package manager

## 🛠️ Installation

1. Clone the repository:

```bash
git clone https://github.com/zeyu-chen/acu-ai-coach.git
cd acu-ai-coach
```

2. Install dependencies:

```bash
npm install
# or
yarn install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
# Edit .env.local with your configuration
```

4. Run the development server:

```bash
npm run dev
# or
yarn dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
src/
├── app/                    # Next.js app router pages
├── components/
│   ├── steps/             # Step-by-step form components
│   │   ├── Step0.tsx      # Landing page
│   │   ├── Step1.tsx      # Personal information
│   │   ├── Step2.tsx      # Dream job selection
│   │   ├── Step3.tsx      # Interests & preferences
│   │   ├── Step4.tsx      # AI coach introduction
│   │   ├── Step5.tsx      # AI chat interface
│   │   └── Step6.tsx      # Completion & QR code
│   ├── ui/                # Reusable UI components
│   ├── Header.tsx         # Application header
│   ├── Footer.tsx         # Application footer
│   └── ProgressIndicator.tsx # Progress tracking
├── lib/                   # Utility functions
public/                    # Static assets
├── images/               # UI images and icons
├── progress-indicator/   # Step indicator icons
└── ...
```

## 🎯 User Journey

1. **Landing Page** - Welcome screen with AI career exploration introduction
2. **Personal Details** - Student information collection (name, email, school)
3. **Dream Job** - Career aspirations and target companies
4. **Interests** - Hobby and interest selection with terms acceptance
5. **AI Coach Intro** - Introduction to AI coaching capabilities
6. **AI Chat** - Interactive conversation with AI career coach
7. **Completion** - QR code for additional resources and email confirmation

## 🎨 Design Features

- **Consistent Purple Theme** (#391460) - ACU brand alignment
- **Glassmorphism Effects** - Modern blur backgrounds for cards
- **Responsive Layout** - 1100px max-width cards with proper spacing
- **Accessible UI** - Proper contrast ratios and keyboard navigation
- **Performance Optimized** - Image sizing and lazy loading

## 🤝 Partnership

Developed in collaboration with **What's On!** for Australian Catholic University recruitment events, helping bridge the gap between high school students and university career opportunities.

## 📝 License

This project is proprietary software developed for Australian Catholic University. All rights reserved.

## 👥 Contributing

This is a private project developed for ACU. For inquiries or collaboration opportunities, please contact the development team.

## 📞 Support

For technical support or questions about the platform:

- **What's On!**: <EMAIL>
- **Phone**: +61 ***********

---

**Built with ❤️ for ACU students exploring their future careers**
